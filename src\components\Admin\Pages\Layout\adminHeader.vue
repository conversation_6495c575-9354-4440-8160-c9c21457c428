<template>
    <div class="header">
        <nav class="navbar navbar-expand-lg bg-white border-bottom py-3 px-2 px-lg-5">
            <div class="container-fluid">
                <a class="navbar-brand MotLogo" href="https://raidot.ai" target="_blank" rel="noopener noreferrer">Rai<span class="text-danger">DOT</span></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fa fa-fw fa-2x fa-bars"></i>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                        <li class="nav-item pe-3">
                            <router-link class="nav-link" :to="{name: 'AdminDashboard'}"><strong>Dashboard</strong></router-link>
                        </li>
                        <li class="nav-item px-3 dropdown">
                            <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="dropdown" @click="debugDropdownClick('Features', $event)"><strong>Features</strong></a>
                            <ul class="dropdown-menu dropdown-menu-end shadow m-0 p-0" style="min-width: 250px">
                                <li class="text-secondary p-3 bg-light border-bottom mb-2"><strong>Management</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'Users'}">Users</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'Workshops'}">Workshops</router-link></li>
                                <li class="text-secondary p-3 bg-light border-bottom border-top mb-2"><strong>Certification</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'EvaluationCertification'}">Evaluation Certification</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'ParticipationCertification'}">Participation Certification</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'AwarenessCertification'}">Awareness Certification</router-link></li>
                            </ul>
                        </li>
                        <li class="nav-item px-3 dropdown">
                            <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-bs-toggle="dropdown" @click="debugDropdownClick('Risk Evaluation', $event)"><strong>Risk Evaluation</strong></a>
                            <ul class="dropdown-menu dropdown-menu-end shadow m-0 p-0" style="min-width: 250px">
                                <li class="text-secondary p-3 bg-light border-bottom mb-2"><strong>Evaluation Sectors</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'Sectors'}">Select an Industry</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'RiskFactors'}">Risk Factors</router-link></li>
                                <li class="text-secondary p-3 bg-light border-bottom border-top mb-2"><strong>Questions</strong></li>
                            <!--<li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'cgptQuestionnaires'}">ChatGPt Evaluation</router-link></li>-->
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'ntQuestionnaires'}">Non Technical</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'etQuestionnaires'}">General Technical</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'etaQuestionnaires'}">Specific Application</router-link></li>
                            </ul>
                        </li>
                        <li class="nav-item px-3 dropdown">
                            <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-bs-toggle="dropdown" @click="debugDropdownClick('Fair Decision Analysis', $event)"><strong>Fair Decision Analysis</strong></a>
                            <ul class="dropdown-menu dropdown-menu-end shadow m-0 p-0" style="min-width: 250px">
                                <li class="text-secondary p-3 bg-light border-bottom mb-2"><strong>Fair Decision</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'FdSectors'}">Select an Industry</router-link></li>
                                <li class="text-secondary p-3 bg-light border-bottom border-top mb-2"><strong>Questions</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'fdQuestionnaires'}">General Fair Decision</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'etaFdQuestionnaires'}">Specific Industry Fair Decision</router-link></li>
                            </ul>
                        </li>
                        <li class="nav-item px-3">
                            <router-link class="nav-link" :to="{name: 'Awareness'}"><strong>Awareness</strong></router-link>
                        </li>
                        <li class="nav-item px-3">
                            <router-link class="nav-link" :to="{name: 'AdminPricingList'}"><strong>Pricing</strong></router-link>
                        </li>
                        <li class="nav-item px-3 dropdown">
                            <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="dropdown" @click="debugDropdownClick('Consultancy', $event)">
                                <strong>Consultancy</strong>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow m-0 p-0" style="min-width: 250px">
                                <li class="text-secondary p-3 bg-light border-bottom mb-2">
                                    <strong>Management</strong>
                                </li>
                                <li class="m-2">
                                    <router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'AdminConsultancySlots'}">
                                        Time Slots
                                    </router-link>
                                </li>
                                <li class="m-2">
                                    <router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'AdminConsultancyBookings'}">
                                        Bookings
                                    </router-link>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <div class="d-flex" role="search">
                        <div class="profile">
                            <div class="dropdown">
                                <div class="profile-info dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" @click="debugDropdownClick('Profile', $event)">
                                    <div class="profile-avatar">
                                        <img :src="'/img/user.png'" alt="">
                                    </div>
                                </div>
                                <ul class="dropdown-menu dropdown-menu-lg-end p-3 shadow-lg" style="min-width: 250px">
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminProfile'}">Profile</router-link></li>
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminEditProfile'}">Update Profile</router-link></li>
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminChangePassword'}">Change Password</router-link></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminNotificationSettings'}">Notification Settings</router-link></li>
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminAccountSettings'}">Account Settings</router-link></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="btn btn-sm btn-danger w-100 rounded-pill" href="javascript:void(0)" @click="Logout">Logout</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>
</template>
<script>


import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";

export default {
    data() {
        return {
            profile: localStorage.getItem('AdminInfo'),
        }
    },
    computed: { },
    methods: {
        debugDropdownClick(dropdownName, event) {
            console.log(`${dropdownName} dropdown clicked:`, event);
            console.log('Event target:', event.target);
            console.log('Current target:', event.currentTarget);
            console.log('Event type:', event.type);
            console.log('Default prevented:', event.defaultPrevented);

            // Check if the dropdown is properly initialized
            const element = event.currentTarget;
            console.log('Element classes:', element.className);
            console.log('Element data-bs-toggle:', element.getAttribute('data-bs-toggle'));

            // Try to get Bootstrap dropdown instance
            if (typeof window.bootstrap !== 'undefined') {
                const dropdownInstance = window.bootstrap.Dropdown.getInstance(element);
                console.log('Bootstrap dropdown instance:', dropdownInstance);

                if (!dropdownInstance) {
                    console.log('No dropdown instance found, trying to create one...');
                    try {
                        const newInstance = new window.bootstrap.Dropdown(element);
                        console.log('New dropdown instance created:', newInstance);
                    } catch (error) {
                        console.error('Error creating dropdown instance:', error);
                    }
                }
            }
        },

        ProfileOption(){
            $('.profile-option').toggleClass('active')
        },

        getMe: function () {
            ApiService.POST(ApiRoutes.AdminProfile, {}, (res) => {
                this.loading = false;
                if (parseInt(res.status) === 200) {
                    this.profile = res.data
                }
            })
        },
        langOption(){
            $('.lang-option').toggleClass('active')
        },
        setLang: function(lang) {
            if(window.localStorage.getItem('lang') != lang) {
                window.localStorage.setItem('lang', lang)
                location.reload();
            }

        },
        toggleMenu: function () {
            this.menuMini =  this.menuMini == false ? true : false;
            $('.sidenav').toggleClass('mini');
            $('.sidebar-toggler').toggleClass('active not-active');
            $('.main_section').toggleClass('full');
            if($('.sidenav').hasClass('active')){
                $('.sidenav').removeClass('active')
            }else{
                $('.sidenav').addClass('active')
            }
        },
        formInit(){
            console.log('formInit called');

            setTimeout(() => {
                console.log('formInit timeout executed');
                if (window.screen.width < 1025) {
                    console.log('Mobile view detected, toggling sidebar');
                    $('.sidenav').toggleClass('mini');
                    $('.main_section').toggleClass('full');
                }
            })

            // Check for jQuery conflicts
            console.log('Setting up mouseup handlers');
            $(document).mouseup(function (e) {
                const container = $('.outsideClick');
                if (!container.is(e.target) && container.has(e.target).length === 0) {
                    console.log('Removing active class from outsideClick');
                    container.removeClass('active');
                }
            });
            $(document).mouseup(function (e) {
                const container = $('.outsideClick2');
                if (!container.is(e.target) && container.has(e.target).length === 0) {
                    console.log('Removing active class from outsideClick2');
                    container.removeClass('active');
                }
            });
        },

        Logout() {
            this.logoutLoading = true;
            ApiService.GET(ApiRoutes.AdminLogout, (res) => {
                this.logoutLoading = false;
                if (parseInt(res.status) === 200) {
                    localStorage.removeItem('AdminJwtToken');
                    localStorage.removeItem('AdminInfo');
                    this.$router.push({name: 'AdminLogin'});
                }
            });
        },
    },
    created() {
        console.log('AdminHeader created');

        // Check for any JavaScript errors
        window.addEventListener('error', (e) => {
            console.error('JavaScript error detected:', e.error);
        });

        // Check for Bootstrap CSS
        const bootstrapCSS = document.querySelector('link[href*="bootstrap"]');
        console.log('Bootstrap CSS found:', !!bootstrapCSS);
        if (bootstrapCSS) {
            console.log('Bootstrap CSS href:', bootstrapCSS.href);
        }
    },
    mounted() {
        console.log('AdminHeader mounted');

        // Check if Bootstrap is loaded
        console.log('Bootstrap available:', typeof window.bootstrap !== 'undefined');
        console.log('jQuery available:', typeof $ !== 'undefined');

        // Check if dropdown elements exist
        const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        console.log('Dropdown elements found:', dropdownElements.length);

        // Add click event listeners to debug dropdown clicks
        dropdownElements.forEach((element, index) => {
            console.log(`Dropdown ${index + 1}:`, element);
            element.addEventListener('click', (e) => {
                console.log(`Dropdown ${index + 1} clicked:`, e.target);
                console.log('Event prevented:', e.defaultPrevented);
            });
        });

        // Check if Bootstrap dropdown is initialized
        setTimeout(() => {
            if (typeof window.bootstrap !== 'undefined') {
                console.log('Initializing Bootstrap dropdowns manually...');
                dropdownElements.forEach((element, index) => {
                    try {
                        const dropdown = new window.bootstrap.Dropdown(element);
                        console.log(`Dropdown ${index + 1} initialized:`, dropdown);
                    } catch (error) {
                        console.error(`Error initializing dropdown ${index + 1}:`, error);
                    }
                });
            } else {
                console.error('Bootstrap is not available - dropdowns will not work');
            }
        }, 100);

        this.getMe()
        this.formInit()
    }
}
</script>
